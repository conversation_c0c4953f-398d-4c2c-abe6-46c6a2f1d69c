#!/bin/bash

# node-labels.sh - List Docker Swarm node labels for all nodes
# Usage: ./node-labels.sh [options]
# Options:
#   -h, --help     Show this help message
#   -v, --verbose  Show verbose output with node details
#   -j, --json     Output in JSON format
#   -f, --filter   Filter nodes by label (e.g., -f "env=production")

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default options
VERBOSE=false
JSON_OUTPUT=false
FILTER=""

# Function to show help
show_help() {
    echo "Usage: $0 [options]"
    echo ""
    echo "List Docker Swarm node labels for all nodes"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -v, --verbose  Show verbose output with node details"
    echo "  -j, --json     Output in JSON format"
    echo "  -f, --filter   Filter nodes by label (e.g., -f \"env=production\")"
    echo ""
    echo "Examples:"
    echo "  $0                           # List all node labels"
    echo "  $0 -v                        # Verbose output with node details"
    echo "  $0 -j                        # JSON format output"
    echo "  $0 -f \"env=production\"       # Filter nodes by label"
    echo "  $0 -v -f \"role=worker\"       # Verbose output for worker nodes"
}

# Function to check if Docker Swarm is active
check_swarm_status() {
    if ! docker info --format '{{.Swarm.LocalNodeState}}' 2>/dev/null | grep -q "active"; then
        echo -e "${RED}Error: Docker Swarm is not active on this node${NC}" >&2
        echo "Please initialize or join a Docker Swarm cluster first." >&2
        exit 1
    fi
}

# Function to get node information
get_node_info() {
    local node_id="$1"
    local format="$2"
    
    if [ "$format" = "json" ]; then
        docker node inspect "$node_id" --format '{{json .}}'
    else
        docker node inspect "$node_id" --format '
Node ID: {{.ID}}
Hostname: {{.Description.Hostname}}
Status: {{.Status.State}}
Availability: {{.Spec.Availability}}
Role: {{.Spec.Role}}
Engine Version: {{.Description.Engine.EngineVersion}}
OS/Arch: {{.Description.Platform.OS}}/{{.Description.Platform.Architecture}}
Labels: {{range $key, $value := .Spec.Labels}}
  {{$key}}={{$value}}{{end}}'
    fi
}

# Function to list node labels in table format
list_node_labels_table() {
    local filter_arg=""
    if [ -n "$FILTER" ]; then
        filter_arg="--filter label=$FILTER"
    fi
    
    echo -e "${CYAN}Docker Swarm Node Labels${NC}"
    echo "=========================="
    echo ""
    
    # Get all nodes
    local nodes
    if [ -n "$FILTER" ]; then
        nodes=$(docker node ls $filter_arg --format "{{.ID}}" 2>/dev/null || true)
        if [ -z "$nodes" ]; then
            echo -e "${YELLOW}No nodes found matching filter: $FILTER${NC}"
            return
        fi
    else
        nodes=$(docker node ls --format "{{.ID}}")
    fi
    
    for node_id in $nodes; do
        local node_info=$(docker node inspect "$node_id" --format '{{.Description.Hostname}} {{.Spec.Role}} {{.Status.State}} {{.Spec.Availability}}')
        local hostname=$(echo "$node_info" | awk '{print $1}')
        local role=$(echo "$node_info" | awk '{print $2}')
        local state=$(echo "$node_info" | awk '{print $3}')
        local availability=$(echo "$node_info" | awk '{print $4}')
        
        # Color coding for role
        local role_color=""
        case "$role" in
            "manager") role_color="${GREEN}" ;;
            "worker") role_color="${BLUE}" ;;
            *) role_color="${NC}" ;;
        esac
        
        # Color coding for state
        local state_color=""
        case "$state" in
            "ready") state_color="${GREEN}" ;;
            "down") state_color="${RED}" ;;
            *) state_color="${YELLOW}" ;;
        esac
        
        echo -e "${PURPLE}Node: ${NC}$hostname ${role_color}($role)${NC} ${state_color}[$state/$availability]${NC}"
        echo -e "${CYAN}ID: ${NC}$node_id"
        
        # Get labels
        local labels=$(docker node inspect "$node_id" --format '{{range $key, $value := .Spec.Labels}}{{$key}}={{$value}}{{"\n"}}{{end}}' | sort)
        
        if [ -n "$labels" ]; then
            echo -e "${YELLOW}Labels:${NC}"
            while IFS= read -r label; do
                if [ -n "$label" ]; then
                    echo "  $label"
                fi
            done <<< "$labels"
        else
            echo -e "${YELLOW}Labels: ${NC}(none)"
        fi
        
        if [ "$VERBOSE" = true ]; then
            local engine_version=$(docker node inspect "$node_id" --format '{{.Description.Engine.EngineVersion}}')
            local os_arch=$(docker node inspect "$node_id" --format '{{.Description.Platform.OS}}/{{.Description.Platform.Architecture}}')
            echo -e "${CYAN}Engine: ${NC}$engine_version"
            echo -e "${CYAN}OS/Arch: ${NC}$os_arch"
        fi
        
        echo ""
    done
}

# Function to output in JSON format
list_node_labels_json() {
    local filter_arg=""
    if [ -n "$FILTER" ]; then
        filter_arg="--filter label=$FILTER"
    fi
    
    local nodes
    if [ -n "$FILTER" ]; then
        nodes=$(docker node ls $filter_arg --format "{{.ID}}" 2>/dev/null || true)
        if [ -z "$nodes" ]; then
            echo "[]"
            return
        fi
    else
        nodes=$(docker node ls --format "{{.ID}}")
    fi
    
    echo "["
    local first=true
    for node_id in $nodes; do
        if [ "$first" = false ]; then
            echo ","
        fi
        first=false
        
        local node_json=$(docker node inspect "$node_id" --format '{{json .}}')
        echo "$node_json" | jq '.'
    done
    echo "]"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -j|--json)
            JSON_OUTPUT=true
            shift
            ;;
        -f|--filter)
            FILTER="$2"
            shift 2
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}" >&2
            echo "Use -h or --help for usage information." >&2
            exit 1
            ;;
    esac
done

# Main execution
main() {
    # Check if Docker is available
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}Error: Docker is not installed or not in PATH${NC}" >&2
        exit 1
    fi
    
    # Check Docker Swarm status
    check_swarm_status
    
    # Output node labels
    if [ "$JSON_OUTPUT" = true ]; then
        # Check if jq is available for JSON formatting
        if ! command -v jq &> /dev/null; then
            echo -e "${YELLOW}Warning: jq not found, JSON output may not be formatted${NC}" >&2
        fi
        list_node_labels_json
    else
        list_node_labels_table
    fi
}

# Run main function
main "$@"
