# docker-compose.yml

version: '3.8'

services:
  traefik:
    image: traefik:v3.0 # 建议使用明确的版本号
    constraints:
      - "node.labels.large=true"
    ports:
      # 发布 HTTP 和 HTTPS 端口
      - target: 80
        published: 80
        protocol: tcp
        mode: host # 使用 host 模式以绕过 Swarm 的路由网格，获得真实客户端 IP
      - target: 443
        published: 443
        protocol: tcp
        mode: host
    volumes:
      # 挂载 Docker socket，让 Traefik 可以监听服务事件
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      # 挂载静态配置文件
      - "./traefik.yml:/etc/traefik/traefik.yml:ro"
      # 挂载 Let's Encrypt 证书存储卷 (重要!)
      - "traefik-letsencrypt:/letsencrypt"
    command:
      # 明确指定配置文件路径
      - "--configfile=/etc/traefik/traefik.yml"
    networks:
      - traefik-proxy
    deploy:
      # !! 核心配置：只部署在 Manager 节点上
      mode: global # 在每个满足条件的节点上运行一个实例
      placement:
        constraints:
          - "node.labels.large == true"
      # 配置资源限制 (生产环境推荐)
      # resources:
      #   limits:
      #     memory: 512M
      #   reservations:
      #     memory: 256M
      labels:
        # --- 通过 Traefik 本身来安全地暴露仪表盘 ---
        - "traefik.enable=true"
        - "traefik.http.routers.dashboard.rule=Host(`traefik.prodbits.com`)" # !! 修改成你的域名
        - "traefik.http.routers.dashboard.entrypoints=websecure"
        - "traefik.http.routers.dashboard.service=api@internal"
        - "traefik.http.routers.dashboard.tls.certresolver=myresolver"
        # 添加 Basic Auth 中间件作为保护
        - "traefik.http.routers.dashboard.middlewares=auth"
        - "traefik.http.middlewares.auth.basicauthP0+r\P0+r\P0+r\.users=admin:$$apr1$$d9i2kEaT$$gHreI1aXzKcc2g5jJeZd3/" # 用户: admin, 密码: password, !! 请务必生成你自己的密码

# 定义网络和卷
networks:
  traefik-proxy:
    external: true

volumes:
  traefik-letsencrypt:
