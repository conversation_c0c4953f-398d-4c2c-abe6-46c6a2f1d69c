# traefik.yml

# 1. 配置入口点
entryPoints:
  web:
    address: ":80"
    # 建议：生产环境重定向所有 HTTP 到 HTTPS
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https
  websecure:
    address: ":443"

# 2. 配置 Docker Provider
providers:
  docker:
    swarmMode: true
    exposedByDefault: false # 安全最佳实践，只暴露明确设置了标签的服务
    network: traefik-proxy  # 告诉 Traefik 在哪个网络里寻找服务

# 3. 配置 API 仪表盘 (为了安全，我们会通过 Traefik 本身来暴露它)
api:
  dashboard: true
  # insecure: true  # 不建议！我们将在 docker-compose.yml 中通过标签来安全地暴露它

# 4. 配置 Let's Encrypt (可选，但强烈推荐)
certificatesResolvers:
  myresolver:
    acme:
      email: "<EMAIL>" # !! 修改成你的邮箱
      storage: "/letsencrypt/acme.json" # 持久化证书
      # 使用 tlsChallenge 更适合高可用环境，因为它不需要在所有节点间共享HTTP挑战
      tlsChallenge: {}