version: '3.8'

services:
  whoami:
    image: traefik/whoami
    networks:
      - traefik-proxy
    deploy:
      replicas: 2 # 部署两个实例，可以分布在任何节点上
      labels:
        # --- Traefik 发现标签 ---
        - "traefik.enable=true"
        - "traefik.http.routers.whoami.rule=Host(`whoami.prodbits.com`)" # !! 修改成你的域名
        - "traefik.http.routers.whoami.entrypoints=websecure"
        - "traefik.http.routers.whoami.tls.certresolver=myresolver"
        - "traefik.http.services.whoami.loadbalancer.server.port=80"

networks:
  traefik-proxy:
    external: true
